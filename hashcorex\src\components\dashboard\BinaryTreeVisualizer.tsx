'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent, Button } from '@/components/ui';
import { Grid } from '@/components/layout';
import { Users, Copy, Share2, TrendingUp, Award } from 'lucide-react';
import { formatCurrency, formatDate, copyToClipboard, getTimeUntilBinaryPayout } from '@/lib/utils';

interface BinaryTreeNode {
  user: {
    id: string;
    email: string;
    createdAt: string;
  };
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
    matchedPoints: number;
  };
  leftChild?: BinaryTreeNode;
  rightChild?: BinaryTreeNode;
}

interface BinaryTreeData {
  treeStructure: BinaryTreeNode;
  statistics: {
    totalDirectReferrals: number;
    leftReferrals: number;
    rightReferrals: number;
    totalCommissions: number;
    binaryPoints: {
      leftPoints: number;
      rightPoints: number;
      matchedPoints: number;
    };
  };
  referralLinks: {
    left: string;
    right: string;
    general: string;
  };
}

export const BinaryTreeVisualizer: React.FC = () => {
  const [treeData, setTreeData] = useState<BinaryTreeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeUntilMatching, setTimeUntilMatching] = useState(getTimeUntilBinaryPayout());

  useEffect(() => {
    fetchTreeData();
    
    // Update countdown every second
    const interval = setInterval(() => {
      setTimeUntilMatching(getTimeUntilBinaryPayout());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchTreeData = async () => {
    try {
      const response = await fetch('/api/referrals/tree?depth=3', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTreeData(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch binary tree data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = async (link: string) => {
    try {
      await copyToClipboard(link);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const TreeNodeComponent: React.FC<{ node: BinaryTreeNode | null; position: string }> = ({ node, position }) => {
    if (!node) {
      return (
        <div className="flex flex-col items-center">
          <div className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-full flex items-center justify-center bg-gray-50">
            <span className="text-gray-400 text-xs">Empty</span>
          </div>
          <p className="text-xs text-gray-500 mt-1 font-medium">{position}</p>
        </div>
      );
    }

    const initials = node.user.email.split('@')[0].substring(0, 2).toUpperCase();

    return (
      <div className="flex flex-col items-center">
        <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 border-2 border-green-300 rounded-full flex flex-col items-center justify-center p-1 shadow-lg">
          <div className="text-sm font-bold text-white">
            {initials}
          </div>
          <div className="text-xs text-green-100 font-medium">
            L:{node.binaryPoints.leftPoints} R:{node.binaryPoints.rightPoints}
          </div>
        </div>
        <p className="text-xs text-gray-700 mt-1 font-medium">{position}</p>
        <p className="text-xs text-gray-500 truncate max-w-20">
          {node.user.email.split('@')[0]}
        </p>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-64 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (!treeData || !treeData.statistics || !treeData.referralLinks) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load binary tree data</p>
          <Button
            onClick={fetchTreeData}
            className="mt-4"
            variant="outline"
          >
            Retry Loading
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {/* Binary Statistics */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Referral Network</h2>
        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Direct Referrals</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {treeData.statistics?.totalDirectReferrals || 0}
                  </p>
                </div>
                <div className="h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Users className="h-7 w-7 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Commissions</p>
                  <p className="text-3xl font-bold text-eco-600">
                    {formatCurrency(treeData.statistics?.totalCommissions || 0)}
                  </p>
                </div>
                <div className="h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-7 w-7 text-eco-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Left Points</p>
                  <p className="text-3xl font-bold text-solar-600">
                    {treeData.statistics?.binaryPoints?.leftPoints || 0}
                  </p>
                </div>
                <div className="h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center">
                  <Award className="h-7 w-7 text-solar-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Right Points</p>
                  <p className="text-3xl font-bold text-solar-600">
                    {treeData.statistics?.binaryPoints?.rightPoints || 0}
                  </p>
                </div>
                <div className="h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center">
                  <Award className="h-7 w-7 text-solar-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      <Grid cols={{ default: 1, lg: 2 }} gap={6}>
        {/* Binary Tree Visualization */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-500" />
              <span>Binary Tree Structure</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative w-full h-96 overflow-hidden">
              <svg className="absolute inset-0 w-full h-full" viewBox="0 0 600 400">
                {/* Connecting Lines */}
                {/* Root to Level 1 */}
                <line x1="300" y1="60" x2="200" y2="140" stroke="#6b7280" strokeWidth="2" />
                <line x1="300" y1="60" x2="400" y2="140" stroke="#6b7280" strokeWidth="2" />

                {/* Level 1 to Level 2 - Left Side */}
                {treeData.treeStructure?.leftChild && (
                  <>
                    <line x1="200" y1="140" x2="120" y2="220" stroke="#6b7280" strokeWidth="2" />
                    <line x1="200" y1="140" x2="280" y2="220" stroke="#6b7280" strokeWidth="2" />
                  </>
                )}

                {/* Level 1 to Level 2 - Right Side */}
                {treeData.treeStructure?.rightChild && (
                  <>
                    <line x1="400" y1="140" x2="320" y2="220" stroke="#6b7280" strokeWidth="2" />
                    <line x1="400" y1="140" x2="480" y2="220" stroke="#6b7280" strokeWidth="2" />
                  </>
                )}
              </svg>

              {/* Nodes positioned absolutely */}
              <div className="absolute inset-0">
                {/* Root Node */}
                <div className="absolute" style={{ left: '250px', top: '10px' }}>
                  <TreeNodeComponent node={treeData.treeStructure || null} position="You" />
                </div>

                {/* Level 1 Nodes */}
                <div className="absolute" style={{ left: '150px', top: '90px' }}>
                  <TreeNodeComponent
                    node={treeData.treeStructure?.leftChild || null}
                    position="Left"
                  />
                </div>
                <div className="absolute" style={{ left: '350px', top: '90px' }}>
                  <TreeNodeComponent
                    node={treeData.treeStructure?.rightChild || null}
                    position="Right"
                  />
                </div>

                {/* Level 2 Nodes */}
                <div className="absolute" style={{ left: '70px', top: '170px' }}>
                  <TreeNodeComponent
                    node={treeData.treeStructure?.leftChild?.leftChild || null}
                    position="L-L"
                  />
                </div>
                <div className="absolute" style={{ left: '230px', top: '170px' }}>
                  <TreeNodeComponent
                    node={treeData.treeStructure?.leftChild?.rightChild || null}
                    position="L-R"
                  />
                </div>
                <div className="absolute" style={{ left: '270px', top: '170px' }}>
                  <TreeNodeComponent
                    node={treeData.treeStructure?.rightChild?.leftChild || null}
                    position="R-L"
                  />
                </div>
                <div className="absolute" style={{ left: '430px', top: '170px' }}>
                  <TreeNodeComponent
                    node={treeData.treeStructure?.rightChild?.rightChild || null}
                    position="R-R"
                  />
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Legend:</h4>
              <div className="text-xs text-gray-600 space-y-1">
                <p>• L: Left side points | R: Right side points</p>
                <p>• Points are added when downline users make investments</p>
                <p>• Binary matching occurs weekly on Saturdays at 15:00 UTC</p>
                <p>• Excess points beyond 2,000 per side are flushed (pressure-out)</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Binary Matching & Referral Links */}
        <div className="space-y-6">
          {/* Next Binary Matching */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="h-5 w-5 text-solar-500" />
                <span>Next Binary Matching</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-4">
                  Weekly binary matching on Saturdays at 15:00 UTC
                </p>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-solar-600">
                      {timeUntilMatching.hours}
                    </div>
                    <div className="text-xs text-gray-500">Hours</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-solar-600">
                      {timeUntilMatching.minutes}
                    </div>
                    <div className="text-xs text-gray-500">Minutes</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-solar-600">
                      {timeUntilMatching.seconds}
                    </div>
                    <div className="text-xs text-gray-500">Seconds</div>
                  </div>
                </div>
                
                <div className="mt-4 p-3 bg-solar-50 rounded-lg">
                  <p className="text-sm text-solar-700">
                    <strong>Potential Match:</strong> {Math.min(treeData.statistics?.binaryPoints?.leftPoints || 0, treeData.statistics?.binaryPoints?.rightPoints || 0)} points
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Referral Links */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Share2 className="h-5 w-5 text-eco-500" />
                <span>Referral Links</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">General Referral Link</label>
                  <div className="flex mt-1">
                    <input
                      type="text"
                      value={treeData.referralLinks.general}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                    />
                    <Button
                      size="sm"
                      onClick={() => handleCopyLink(treeData.referralLinks.general)}
                      className="rounded-l-none"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">Left Side Link</label>
                  <div className="flex mt-1">
                    <input
                      type="text"
                      value={treeData.referralLinks.left}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                    />
                    <Button
                      size="sm"
                      onClick={() => handleCopyLink(treeData.referralLinks.left)}
                      className="rounded-l-none"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">Right Side Link</label>
                  <div className="flex mt-1">
                    <input
                      type="text"
                      value={treeData.referralLinks.right}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                    />
                    <Button
                      size="sm"
                      onClick={() => handleCopyLink(treeData.referralLinks.right)}
                      className="rounded-l-none"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-xs text-blue-700">
                  <strong>Tip:</strong> Share your referral links to build your binary network. 
                  New users will be placed automatically in your weaker leg.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </Grid>
    </div>
  );
};
