const { default: fetch } = require('node-fetch');

async function testReferralAPI() {
  try {
    console.log('=== TESTING REFERRAL API ENDPOINT ===');
    
    // First login to get session
    const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Test123!@#'
      }),
    });

    const loginData = await loginResponse.json();
    console.log('Login response:', loginData.success ? 'Success' : 'Failed');

    if (!loginData.success) {
      console.log('Login failed:', loginData.error);
      return;
    }

    // Extract cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('Cookies received:', cookies ? 'Yes' : 'No');

    // Test the referral tree API
    const treeResponse = await fetch('http://localhost:3000/api/referrals/tree?depth=3&enhanced=true', {
      method: 'GET',
      headers: {
        'Cookie': cookies || '',
        'Content-Type': 'application/json',
      },
    });

    const treeData = await treeResponse.json();
    console.log('\n=== API RESPONSE ===');
    console.log('Success:', treeData.success);
    
    if (treeData.success) {
      console.log('Statistics:', JSON.stringify(treeData.data.statistics, null, 2));
    } else {
      console.log('Error:', treeData.error);
    }

  } catch (error) {
    console.error('Test error:', error);
  }
}

testReferralAPI();
