import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin settings
  const adminSettings = [
    { key: 'THS_PRICE', value: '50' },
    { key: 'ROI_MIN', value: '0.6' },
    { key: 'ROI_MAX', value: '1.1' },
    { key: 'MINIMUM_PURCHASE', value: '50' },
    { key: 'MINIMUM_WITHDRAWAL', value: '10' },
    { key: 'DIRECT_REFERRAL_BONUS', value: '10' },
    { key: 'BINARY_POOL_PERCENTAGE', value: '30' },
    { key: 'MAX_BINARY_POINTS_PER_SIDE', value: '2000' },
    { key: 'PLATFORM_NAME', value: 'HashCoreX' },
    { key: 'PLATFORM_EMAIL', value: '<EMAIL>' },
    { key: 'MAINTENANCE_MODE', value: 'false' },
    { key: 'REGISTRATION_ENABLED', value: 'true' },
    { key: 'KYC_REQUIRED', value: 'true' },
    { key: 'WITHDRAWAL_ENABLED', value: 'true' },
  ];

  console.log('📝 Creating admin settings...');
  for (const setting of adminSettings) {
    await prisma.adminSettings.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: {
        key: setting.key,
        value: setting.value,
      },
    });
  }

  // Create admin user
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.ADMIN_PASSWORD || 'HashCoreX2024!';
  const hashedPassword = await bcrypt.hash(adminPassword, 12);

  console.log('👤 Creating admin user...');
  const adminUser = await prisma.user.upsert({
    where: { email: adminEmail },
    update: {},
    create: {
      email: adminEmail,
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      referralId: 'ADMIN_REF_001',
      kycStatus: 'APPROVED',
      isActive: true,
    },
  });

  // Create initial binary pool
  console.log('💰 Creating initial binary pool...');
  await prisma.binaryPool.create({
    data: {
      totalPool: 0,
      distributedPool: 0,
      remainingPool: 0,
    },
  });

  // Create demo users for testing (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log('🧪 Creating demo users for testing...');
    
    const demoUsers = [
      {
        email: '<EMAIL>',
        password: 'Demo123!',
        firstName: 'Demo',
        lastName: 'User 1',
        referralId: 'DEMO_REF_001',
      },
      {
        email: '<EMAIL>',
        password: 'Demo123!',
        firstName: 'Demo',
        lastName: 'User 2',
        referralId: 'DEMO_REF_002',
      },
      {
        email: '<EMAIL>',
        password: 'Demo123!',
        firstName: 'Demo',
        lastName: 'User 3',
        referralId: 'DEMO_REF_003',
      },
    ];

    for (const demoUser of demoUsers) {
      const hashedDemoPassword = await bcrypt.hash(demoUser.password, 12);
      
      await prisma.user.upsert({
        where: { email: demoUser.email },
        update: {},
        create: {
          email: demoUser.email,
          password: hashedDemoPassword,
          firstName: demoUser.firstName,
          lastName: demoUser.lastName,
          referralId: demoUser.referralId,
          kycStatus: 'PENDING',
          isActive: true,
        },
      });
    }

    // Create demo mining units
    console.log('⛏️ Creating demo mining units...');
    const demoUser1 = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
    });

    if (demoUser1) {
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1);

      await prisma.miningUnit.create({
        data: {
          userId: demoUser1.id,
          thsAmount: 1.0,
          investmentAmount: 50,
          dailyROI: 0.8,
          expiryDate,
          status: 'ACTIVE',
        },
      });

      // Create demo transactions
      await prisma.transaction.create({
        data: {
          userId: demoUser1.id,
          type: 'PURCHASE',
          amount: 50,
          description: 'Mining unit purchase - 1.0 TH/s',
          status: 'COMPLETED',
        },
      });

      await prisma.transaction.create({
        data: {
          userId: demoUser1.id,
          type: 'MINING_EARNINGS',
          amount: 0.4,
          description: 'Daily mining earnings',
          status: 'COMPLETED',
        },
      });

      // Create binary points
      await prisma.binaryPoints.create({
        data: {
          userId: demoUser1.id,
          leftPoints: 50,
          rightPoints: 25,
          matchedPoints: 25,
        },
      });
    }
  }

  // Log system initialization
  await prisma.systemLog.create({
    data: {
      action: 'SYSTEM_INITIALIZED',
      details: JSON.stringify({
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0',
      }),
    },
  });

  console.log('✅ Database seeding completed successfully!');
  console.log(`📧 Admin email: ${adminEmail}`);
  console.log(`🔑 Admin password: ${adminPassword}`);
  
  if (process.env.NODE_ENV === 'development') {
    console.log('🧪 Demo users created:');
    console.log('   - <EMAIL> / Demo123!');
    console.log('   - <EMAIL> / Demo123!');
    console.log('   - <EMAIL> / Demo123!');
  }
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
