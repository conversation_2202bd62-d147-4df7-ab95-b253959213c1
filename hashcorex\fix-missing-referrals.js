const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixMissingReferrals() {
  try {
    console.log('=== FIXING MISSING REFERRAL RECORDS ===');
    
    // Find all users who have a referrerId but no corresponding referral record
    const usersWithMissingReferrals = await prisma.user.findMany({
      where: {
        referrerId: { not: null },
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        referrerId: true,
        createdAt: true,
      },
    });

    console.log(`Found ${usersWithMissingReferrals.length} users with referrerId set`);

    for (const user of usersWithMissingReferrals) {
      // Check if referral record exists
      const existingReferral = await prisma.referral.findFirst({
        where: {
          referrerId: user.referrerId,
          referredId: user.id,
        },
      });

      if (!existingReferral) {
        console.log(`\n⚠️  Missing referral record for: ${user.email}`);
        console.log(`   Referrer ID: ${user.referrerId}`);
        
        // Find the referrer
        const referrer = await prisma.user.findUnique({
          where: { id: user.referrerId },
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            leftReferralId: true,
            rightReferralId: true,
          },
        });

        if (referrer) {
          console.log(`   Referrer: ${referrer.email} (${referrer.firstName} ${referrer.lastName})`);
          
          // Determine placement side based on existing referral structure
          let placementSide = 'LEFT'; // Default to left
          
          // Check if left side is already taken
          if (referrer.leftReferralId) {
            // Check if right side is available
            if (!referrer.rightReferralId) {
              placementSide = 'RIGHT';
            } else {
              // Both sides taken, need to find optimal placement
              // For now, let's place in left (this would need more complex logic in production)
              console.log(`   ⚠️  Both sides already taken for referrer ${referrer.email}`);
              console.log(`   Left: ${referrer.leftReferralId}, Right: ${referrer.rightReferralId}`);
              console.log(`   Placing in LEFT side anyway (may need manual review)`);
              placementSide = 'LEFT';
            }
          }

          console.log(`   Placing on ${placementSide} side`);

          // Create the missing referral record
          await prisma.referral.create({
            data: {
              referrerId: user.referrerId,
              referredId: user.id,
              placementSide: placementSide,
            },
          });

          // Update the referrer's left/right referral ID if not already set
          const updateData = {};
          if (placementSide === 'LEFT' && !referrer.leftReferralId) {
            updateData.leftReferralId = user.id;
          } else if (placementSide === 'RIGHT' && !referrer.rightReferralId) {
            updateData.rightReferralId = user.id;
          }

          if (Object.keys(updateData).length > 0) {
            await prisma.user.update({
              where: { id: user.referrerId },
              data: updateData,
            });
            console.log(`   ✅ Updated referrer's ${placementSide.toLowerCase()}ReferralId`);
          }

          console.log(`   ✅ Created referral record for ${user.email}`);
        } else {
          console.log(`   ❌ Referrer not found for user ${user.email}`);
        }
      } else {
        console.log(`✅ Referral record exists for: ${user.email}`);
      }
    }

    console.log('\n=== VERIFICATION ===');
    
    // Verify <EMAIL> specifically
    const malcom = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: { id: true, email: true },
    });

    if (malcom) {
      const malcolmReferrals = await prisma.referral.findMany({
        where: { referrerId: malcom.id },
        include: {
          referred: {
            select: {
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      console.log(`\nMalcom's referral records: ${malcolmReferrals.length}`);
      malcolmReferrals.forEach((ref, index) => {
        console.log(`${index + 1}. ${ref.referred.email} - ${ref.placementSide}`);
      });
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixMissingReferrals();
