'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent, Button, Input } from '@/components/ui';
import {
  Shield,
  Eye,
  Check,
  X,
  Download,
  FileText,
  User,
  Calendar,
  AlertTriangle,
  Search,
  Filter,
  RefreshCw,
  ZoomIn
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface KYCDocument {
  id: string;
  userId: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    referralId: string;
  };
  documentType: string;
  documentUrl: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  submittedAt: string;
  reviewedAt?: string;
  rejectionReason?: string;
}

interface KYCUser {
  userId: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    referralId: string;
  };
  documents: KYCDocument[];
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  submittedAt: string;
}

export const KYCReview: React.FC = () => {
  const [users, setUsers] = useState<KYCUser[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<KYCUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUser, setSelectedUser] = useState<KYCUser | null>(null);
  const [selectedDocument, setSelectedDocument] = useState<KYCDocument | null>(null);
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [processing, setProcessing] = useState(false);
  const [documentViewerOpen, setDocumentViewerOpen] = useState(false);

  // Search and filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'ALL' | 'PENDING' | 'APPROVED' | 'REJECTED'>('ALL');

  useEffect(() => {
    fetchKYCData();
  }, []);

  useEffect(() => {
    filterUsers();
  }, [users, searchTerm, statusFilter]);

  const fetchKYCData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/kyc/all', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUsers(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch KYC data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = users;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.user.referralId.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'ALL') {
      filtered = filtered.filter(user => user.status === statusFilter);
    }

    setFilteredUsers(filtered);
  };

  const handleReview = async (userId: string, action: 'approve' | 'reject', reason?: string) => {
    try {
      setProcessing(true);
      const response = await fetch('/api/admin/kyc/review', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          userId,
          action: action.toUpperCase(),
          rejectionReason: reason,
        }),
      });

      if (response.ok) {
        fetchKYCData(); // Refresh the list
        setSelectedUser(null);
        setSelectedDocument(null);
        setReviewAction(null);
        setRejectionReason('');
      }
    } catch (error) {
      console.error('Failed to review KYC document:', error);
    } finally {
      setProcessing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      PENDING: 'bg-yellow-900 text-yellow-300 border border-yellow-700',
      APPROVED: 'bg-green-900 text-green-300 border border-green-700',
      REJECTED: 'bg-red-900 text-red-300 border border-red-700',
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[status as keyof typeof colors]}`}>
        {status}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-700 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-slate-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">KYC Review</h1>
          <p className="text-slate-400 mt-1">Review and approve user KYC documents</p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={fetchKYCData}
            disabled={loading}
            className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <div className="flex items-center gap-2 text-sm text-slate-400">
            <AlertTriangle className="h-4 w-4 text-orange-400" />
            {filteredUsers.filter(u => u.status === 'PENDING').length} pending reviews
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search by name, email, or user ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="md:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="ALL">All Status</option>
                <option value="PENDING">Pending</option>
                <option value="APPROVED">Approved</option>
                <option value="REJECTED">Rejected</option>
              </select>
            </div>
          </div>

          {/* Results Summary */}
          <div className="mt-4 flex items-center gap-4 text-sm text-slate-400">
            <span>Showing {filteredUsers.length} of {users.length} users</span>
            <span>•</span>
            <span>{filteredUsers.filter(u => u.status === 'PENDING').length} pending</span>
            <span>•</span>
            <span>{filteredUsers.filter(u => u.status === 'APPROVED').length} approved</span>
            <span>•</span>
            <span>{filteredUsers.filter(u => u.status === 'REJECTED').length} rejected</span>
          </div>
        </CardContent>
      </Card>

      {/* KYC Users */}
      <div className="grid gap-6">
        {filteredUsers.length === 0 ? (
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="p-12 text-center">
              <Shield className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-white mb-2">
                {users.length === 0 ? 'No KYC Submissions' : 'No Results Found'}
              </h3>
              <p className="text-slate-400">
                {users.length === 0
                  ? 'No users have submitted KYC documents yet.'
                  : 'Try adjusting your search or filter criteria.'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredUsers.map((userKyc) => (
            <Card key={userKyc.userId} className="bg-slate-800 border-slate-700 hover:bg-slate-750 transition-colors">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-slate-400" />
                        <span className="font-medium text-white">
                          {userKyc.user.firstName} {userKyc.user.lastName}
                        </span>
                      </div>
                      {getStatusBadge(userKyc.status)}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-400 mb-4">
                      <div>
                        <span className="font-medium text-slate-300">Email:</span> {userKyc.user.email}
                      </div>
                      <div>
                        <span className="font-medium text-slate-300">User ID:</span> {userKyc.user.referralId}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span className="font-medium text-slate-300">Submitted:</span> {formatDate(userKyc.submittedAt)}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 text-sm text-slate-400 mb-4">
                      <FileText className="h-4 w-4" />
                      <span className="font-medium text-slate-300">Documents:</span> {userKyc.documents.length} uploaded
                    </div>

                    {/* Document List */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 mb-4">
                      {userKyc.documents.map((doc) => (
                        <div key={doc.id} className="bg-slate-700 rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-xs font-medium text-slate-300">
                              {doc.documentType === 'SELFIE' ? 'Selfie' :
                               `${doc.idType} - ${doc.documentSide}`}
                            </span>
                            {getStatusBadge(doc.status)}
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedDocument(doc);
                                setDocumentViewerOpen(true);
                              }}
                              className="border-slate-600 text-slate-300 hover:bg-slate-600 hover:text-white text-xs px-2 py-1"
                            >
                              <ZoomIn className="h-3 w-3 mr-1" />
                              View
                            </Button>
                          </div>
                          {doc.rejectionReason && (
                            <p className="text-xs text-red-400 mt-2 truncate" title={doc.rejectionReason}>
                              {doc.rejectionReason}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 ml-4">
                    {userKyc.status === 'PENDING' && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(userKyc);
                            setReviewAction('approve');
                          }}
                          className="border-green-600 text-green-400 hover:text-green-300 hover:bg-green-900/20"
                        >
                          <Check className="h-4 w-4 mr-1" />
                          Approve
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedUser(userKyc);
                            setReviewAction('reject');
                          }}
                          className="border-red-600 text-red-400 hover:text-red-300 hover:bg-red-900/20"
                        >
                          <X className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Document Viewer Modal */}
      {documentViewerOpen && selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-4 z-50">
          <div className="bg-slate-800 border border-slate-700 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-4 border-b border-slate-700">
              <h3 className="text-lg font-semibold text-white">
                Document Viewer - {selectedDocument.documentType === 'SELFIE' ? 'Selfie' :
                `${selectedDocument.idType} ${selectedDocument.documentSide}`}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setDocumentViewerOpen(false);
                  setSelectedDocument(null);
                }}
                className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-4 max-h-[calc(90vh-120px)] overflow-auto">
              <img
                src={selectedDocument.documentUrl}
                alt="KYC Document"
                className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
              />
            </div>
          </div>
        </div>
      )}

      {/* Review Modal */}
      {selectedUser && reviewAction && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 z-50">
          <div className="bg-slate-800 border border-slate-700 rounded-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold mb-4 text-white">
              {reviewAction === 'approve' ? 'Approve KYC Submission' : 'Reject KYC Submission'}
            </h3>

            <div className="mb-4">
              <p className="text-slate-400 mb-2">
                User: <span className="font-medium text-white">{selectedUser.user.firstName} {selectedUser.user.lastName}</span>
              </p>
              <p className="text-slate-400 mb-2">
                Email: <span className="font-medium text-white">{selectedUser.user.email}</span>
              </p>
              <p className="text-slate-400">
                Documents: <span className="font-medium text-white">{selectedUser.documents.length} uploaded</span>
              </p>
            </div>

            {reviewAction === 'reject' && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Rejection Reason *
                </label>
                <textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-white placeholder-slate-400 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  rows={3}
                  placeholder="Please provide a reason for rejection..."
                  required
                />
              </div>
            )}

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedUser(null);
                  setReviewAction(null);
                  setRejectionReason('');
                }}
                disabled={processing}
                className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-white"
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleReview(
                  selectedUser.userId,
                  reviewAction,
                  reviewAction === 'reject' ? rejectionReason : undefined
                )}
                disabled={processing || (reviewAction === 'reject' && !rejectionReason.trim())}
                loading={processing}
                className={reviewAction === 'approve'
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-red-600 hover:bg-red-700 text-white'
                }
              >
                {reviewAction === 'approve' ? 'Approve' : 'Reject'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
