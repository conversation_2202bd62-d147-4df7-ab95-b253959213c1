{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount);\n}\n\nexport function formatNumber(num: number, decimals = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(num);\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d);\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  if (navigator.clipboard) {\n    return navigator.clipboard.writeText(text);\n  }\n  \n  // Fallback for older browsers\n  const textArea = document.createElement('textarea');\n  textArea.value = text;\n  document.body.appendChild(textArea);\n  textArea.focus();\n  textArea.select();\n  \n  try {\n    document.execCommand('copy');\n    return Promise.resolve();\n  } catch (err) {\n    return Promise.reject(err);\n  } finally {\n    document.body.removeChild(textArea);\n  }\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n}\n\nexport function calculateROI(\n  investment: number,\n  dailyRate: number,\n  days: number\n): number {\n  return investment * (dailyRate / 100) * days;\n}\n\nexport function calculateTHSPrice(ths: number, pricePerTHS: number): number {\n  return ths * pricePerTHS;\n}\n\nexport function formatTHS(ths: number): string {\n  if (ths >= 1000) {\n    return `${(ths / 1000).toFixed(1)}K TH/s`;\n  }\n  return `${ths.toFixed(2)} TH/s`;\n}\n\nexport function getTimeUntilNextPayout(): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextSaturday = new Date();\n  \n  // Set to next Saturday at 15:00 UTC\n  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n  nextSaturday.setUTCHours(15, 0, 0, 0);\n  \n  // If it's already past Saturday 15:00, move to next week\n  if (now > nextSaturday) {\n    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n  }\n  \n  const diff = nextSaturday.getTime() - now.getTime();\n  \n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { days, hours, minutes, seconds };\n}\n\nexport function getTimeUntilBinaryPayout(): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextSaturday = new Date();\n\n  // Set to next Saturday at 15:00 UTC\n  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n  nextSaturday.setUTCHours(15, 0, 0, 0);\n\n  // If it's already past Saturday 15:00, move to next week\n  if (now > nextSaturday) {\n    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n  }\n\n  const diff = nextSaturday.getTime() - now.getTime();\n\n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n\n  return { days, hours, minutes, seconds };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW,EAAE,WAAW,CAAC;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,UAAU,SAAS,EAAE;QACvB,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;IACvC;IAEA,8BAA8B;IAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;IACxC,SAAS,KAAK,GAAG;IACjB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,SAAS,KAAK;IACd,SAAS,MAAM;IAEf,IAAI;QACF,SAAS,WAAW,CAAC;QACrB,OAAO,QAAQ,OAAO;IACxB,EAAE,OAAO,KAAK;QACZ,OAAO,QAAQ,MAAM,CAAC;IACxB,SAAU;QACR,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aACd,UAAkB,EAClB,SAAiB,EACjB,IAAY;IAEZ,OAAO,aAAa,CAAC,YAAY,GAAG,IAAI;AAC1C;AAEO,SAAS,kBAAkB,GAAW,EAAE,WAAmB;IAChE,OAAO,MAAM;AACf;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,OAAO,MAAM;QACf,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC;IAC3C;IACA,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC;AACjC;AAEO,SAAS;IAMd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,oCAAoC;IACpC,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;IAC/D,aAAa,WAAW,CAAC,IAAI,GAAG,GAAG;IAEnC,yDAAyD;IACzD,IAAI,MAAM,cAAc;QACtB,aAAa,UAAU,CAAC,aAAa,UAAU,KAAK;IACtD;IAEA,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IACzE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAM;QAAO;QAAS;IAAQ;AACzC;AAEO,SAAS;IAMd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,oCAAoC;IACpC,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;IAC/D,aAAa,WAAW,CAAC,IAAI,GAAG,GAAG;IAEnC,yDAAyD;IACzD,IAAI,MAAM,cAAc;QACtB,aAAa,UAAU,CAAC,aAAa,UAAU,KAAK;IACtD;IAEA,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IACzE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAM;QAAO;QAAS;IAAQ;AACzC", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/types/index.ts"], "sourcesContent": ["// User Types\nexport interface User {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  firebaseUid?: string;\n  referralId: string;\n  leftReferralId?: string;\n  rightReferralId?: string;\n  role: UserRole;\n  kycStatus: KYCStatus;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport enum UserRole {\n  USER = 'USER',\n  ADMIN = 'ADMIN'\n}\n\nexport interface UserProfile extends User {\n  totalTHS: number;\n  walletBalance: number;\n  totalEarnings: number;\n  activeUnits: number;\n}\n\n// KYC Types\nexport enum KYCStatus {\n  PENDING = 'PENDING',\n  APPROVED = 'APPROVED',\n  REJECTED = 'REJECTED'\n}\n\nexport interface KYCDocument {\n  id: string;\n  userId: string;\n  documentType: DocumentType;\n  idType?: IDType;\n  documentSide?: DocumentSide;\n  filePath: string;\n  status: KYCStatus;\n  reviewedAt?: Date;\n  createdAt: Date;\n}\n\nexport enum DocumentType {\n  ID_DOCUMENT = 'ID_DOCUMENT',\n  SELFIE = 'SELFIE'\n}\n\nexport enum IDType {\n  NATIONAL_ID = 'NATIONAL_ID',\n  PASSPORT = 'PASSPORT',\n  DRIVING_LICENSE = 'DRIVING_LICENSE'\n}\n\nexport enum DocumentSide {\n  FRONT = 'FRONT',\n  BACK = 'BACK'\n}\n\n// Mining Unit Types\nexport interface MiningUnit {\n  id: string;\n  userId: string;\n  thsAmount: number;\n  investmentAmount: number;\n  startDate: Date;\n  expiryDate: Date;\n  dailyROI: number;\n  totalEarned: number;\n  status: MiningUnitStatus;\n  createdAt: Date;\n}\n\nexport enum MiningUnitStatus {\n  ACTIVE = 'ACTIVE',\n  EXPIRED = 'EXPIRED'\n}\n\n// Transaction Types\nexport interface Transaction {\n  id: string;\n  userId: string;\n  type: TransactionType;\n  amount: number;\n  description: string;\n  status: TransactionStatus;\n  createdAt: Date;\n}\n\nexport enum TransactionType {\n  MINING_EARNINGS = 'MINING_EARNINGS',\n  DIRECT_REFERRAL = 'DIRECT_REFERRAL',\n  BINARY_BONUS = 'BINARY_BONUS',\n  DEPOSIT = 'DEPOSIT',\n  WITHDRAWAL = 'WITHDRAWAL',\n  PURCHASE = 'PURCHASE'\n}\n\nexport enum TransactionStatus {\n  PENDING = 'PENDING',\n  COMPLETED = 'COMPLETED',\n  FAILED = 'FAILED',\n  CANCELLED = 'CANCELLED'\n}\n\n// Referral Types\nexport interface Referral {\n  id: string;\n  referrerId: string;\n  referredId: string;\n  placementSide: 'LEFT' | 'RIGHT';\n  commissionEarned: number;\n  createdAt: Date;\n}\n\nexport interface BinaryPoints {\n  id: string;\n  userId: string;\n  leftPoints: number;\n  rightPoints: number;\n  matchedPoints: number;\n  flushDate?: Date;\n  createdAt: Date;\n}\n\n// Withdrawal Types\nexport interface WithdrawalRequest {\n  id: string;\n  userId: string;\n  amount: number;\n  usdtAddress: string;\n  status: WithdrawalStatus;\n  processedAt?: Date;\n  createdAt: Date;\n}\n\nexport enum WithdrawalStatus {\n  PENDING = 'PENDING',\n  APPROVED = 'APPROVED',\n  REJECTED = 'REJECTED',\n  COMPLETED = 'COMPLETED'\n}\n\nexport enum DepositStatus {\n  PENDING = 'PENDING',\n  VERIFYING = 'VERIFYING',\n  CONFIRMED = 'CONFIRMED',\n  COMPLETED = 'COMPLETED',\n  FAILED = 'FAILED',\n  REJECTED = 'REJECTED'\n}\n\n// Wallet Types\nexport interface WalletBalance {\n  id: string;\n  userId: string;\n  availableBalance: number;\n  pendingBalance: number;\n  totalDeposits: number;\n  totalWithdrawals: number;\n  totalEarnings: number;\n  lastUpdated: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface DepositTransaction {\n  id: string;\n  userId: string;\n  transactionId: string;\n  amount: number;\n  usdtAmount: number;\n  tronAddress: string;\n  senderAddress?: string;\n  status: DepositStatus;\n  blockNumber?: string;\n  blockTimestamp?: Date;\n  confirmations: number;\n  verifiedAt?: Date;\n  processedAt?: Date;\n  failureReason?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  user?: User;\n}\n\n// Admin Types\nexport interface AdminSettings {\n  key: string;\n  value: string;\n  updatedAt: Date;\n}\n\nexport interface AdminStats {\n  totalUsers: number;\n  activeUsers: number;\n  totalTHSSold: number;\n  totalEarnings: number;\n  pendingKYC: number;\n  pendingWithdrawals: number;\n}\n\n// API Response Types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\n// Form Types\nexport interface LoginForm {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterForm {\n  email: string;\n  password: string;\n  confirmPassword: string;\n  referralCode?: string;\n}\n\nexport interface PurchaseForm {\n  thsAmount: number;\n  investmentAmount: number;\n}\n\nexport interface WithdrawalForm {\n  amount: number;\n  usdtAddress: string;\n}\n\nexport interface DepositForm {\n  transactionId: string;\n  amount?: number; // Optional, will be verified from blockchain\n}\n\n// Dashboard Types\nexport interface DashboardStats {\n  totalTHS: number;\n  estimatedEarnings: {\n    next7Days: number;\n    next30Days: number;\n    next365Days: number;\n  };\n  walletBalance: number;\n  activeUnits: number;\n  totalEarnings: number;\n}\n\nexport interface BinaryTreeNode {\n  userId: string;\n  email: string;\n  leftChild?: BinaryTreeNode;\n  rightChild?: BinaryTreeNode;\n  totalPoints: number;\n  isActive: boolean;\n}\n\n// Component Props Types\nexport interface ButtonProps {\n  variant?: 'primary' | 'secondary' | 'success' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n  disabled?: boolean;\n  loading?: boolean;\n  children: React.ReactNode;\n  onClick?: () => void;\n  type?: 'button' | 'submit' | 'reset';\n}\n\nexport interface CardProps {\n  title?: string;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport interface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n}\n\n// Utility Types\nexport type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\nexport type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;\n"], "names": [], "mappings": "AAAA,aAAa;;;;;;;;;;;;;AAgBN,IAAA,AAAK,kCAAA;;;WAAA;;AAaL,IAAA,AAAK,mCAAA;;;;WAAA;;AAkBL,IAAA,AAAK,sCAAA;;;WAAA;;AAKL,IAAA,AAAK,gCAAA;;;;WAAA;;AAML,IAAA,AAAK,sCAAA;;;WAAA;;AAmBL,IAAA,AAAK,0CAAA;;;WAAA;;AAgBL,IAAA,AAAK,yCAAA;;;;;;;WAAA;;AASL,IAAA,AAAK,2CAAA;;;;;WAAA;;AAsCL,IAAA,AAAK,0CAAA;;;;;WAAA;;AAOL,IAAA,AAAK,uCAAA;;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/hooks/useAuth';\nimport { useRouter } from 'next/navigation';\nimport { DashboardLayout } from '@/components/dashboard/DashboardLayout';\nimport { DashboardOverview } from '@/components/dashboard/DashboardOverview';\nimport { PurchaseMiningUnit } from '@/components/dashboard/PurchaseMiningUnit';\nimport { EarningsTracker } from '@/components/dashboard/EarningsTracker';\nimport { WalletDashboard } from '@/components/dashboard/WalletDashboard';\nimport { ReactFlowBinaryTree } from '@/components/dashboard/ReactFlowBinaryTree';\nimport { KYCPortal } from '@/components/dashboard/KYCPortal';\nimport { MiningUnitsTable } from '@/components/dashboard/MiningUnitsTable';\nimport { SupportCenter } from '@/components/dashboard/SupportCenter';\nimport { Loading } from '@/components/ui';\n\nexport default function DashboardPage() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState('overview');\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Loading size=\"lg\" text=\"Loading dashboard...\" />\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'overview':\n        return <DashboardOverview />;\n      case 'mining':\n        return (\n          <div className=\"space-y-6\">\n            <PurchaseMiningUnit onPurchaseComplete={() => window.location.reload()} />\n            <MiningUnitsTable />\n          </div>\n        );\n      case 'earnings':\n        return <EarningsTracker />;\n      case 'wallet':\n        return <WalletDashboard />;\n      case 'referrals':\n        return <ReactFlowBinaryTree />;\n      case 'kyc':\n        return <KYCPortal />;\n      case 'support':\n        return <SupportCenter />;\n      default:\n        return <DashboardOverview />;\n    }\n  };\n\n  return (\n    <DashboardLayout activeTab={activeTab} onTabChange={setActiveTab}>\n      {renderTabContent()}\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAdA;;;;;;;;;;;;;;;AAgBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAK;gBAAK,MAAK;;;;;;;;;;;IAG9B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oJAAA,CAAA,oBAAiB;;;;;YAC3B,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,qJAAA,CAAA,qBAAkB;4BAAC,oBAAoB,IAAM,OAAO,QAAQ,CAAC,MAAM;;;;;;sCACpE,8OAAC,mJAAA,CAAA,mBAAgB;;;;;;;;;;;YAGvB,KAAK;gBACH,qBAAO,8OAAC,kJAAA,CAAA,kBAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,kJAAA,CAAA,kBAAe;;;;;YACzB,KAAK;gBACH,qBAAO,8OAAC,sJAAA,CAAA,sBAAmB;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,4IAAA,CAAA,YAAS;;;;;YACnB,KAAK;gBACH,qBAAO,8OAAC,gJAAA,CAAA,gBAAa;;;;;YACvB;gBACE,qBAAO,8OAAC,oJAAA,CAAA,oBAAiB;;;;;QAC7B;IACF;IAEA,qBACE,8OAAC,kJAAA,CAAA,kBAAe;QAAC,WAAW;QAAW,aAAa;kBACjD;;;;;;AAGP", "debugId": null}}]}