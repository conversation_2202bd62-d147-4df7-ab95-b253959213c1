import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { withdrawalDb, transactionDb, adminSettingsDb, systemLogDb } from '@/lib/database';
import { userDb } from '@/lib/database';

// POST - Create withdrawal request
export async function POST(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { amount, usdtAddress } = body;

    // Validation
    if (!amount || !usdtAddress || amount <= 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid amount or USDT address' },
        { status: 400 }
      );
    }

    // Validate USDT TRC20 address format (basic validation)
    if (!usdtAddress.match(/^T[A-Za-z1-9]{33}$/)) {
      return NextResponse.json(
        { success: false, error: 'Invalid USDT TRC20 address format' },
        { status: 400 }
      );
    }

    // Check KYC status
    const userDetails = await userDb.findByEmail(user.email);
    if (!userDetails || userDetails.kycStatus !== 'APPROVED') {
      return NextResponse.json(
        { success: false, error: 'KYC verification required for withdrawals' },
        { status: 400 }
      );
    }

    // Get withdrawal settings
    const minWithdrawal = parseFloat(await adminSettingsDb.get('minWithdrawalAmount') || '10');
    const fixedFee = parseFloat(await adminSettingsDb.get('withdrawalFeeFixed') || '3');
    const percentageFee = parseFloat(await adminSettingsDb.get('withdrawalFeePercentage') || '1');

    if (amount < minWithdrawal) {
      return NextResponse.json(
        { success: false, error: `Minimum withdrawal amount is $${minWithdrawal}` },
        { status: 400 }
      );
    }

    // Calculate total fees
    const percentageFeeAmount = (amount * percentageFee) / 100;
    const totalFees = fixedFee + percentageFeeAmount;
    const totalDeduction = amount + totalFees;
    const netAmount = amount; // User receives the requested amount, fees are deducted separately

    // Calculate current wallet balance
    const transactions = await transactionDb.findByUserId(user.id);
    let balance = 0;
    
    for (const transaction of transactions) {
      if (transaction.status === 'COMPLETED') {
        switch (transaction.type) {
          case 'MINING_EARNINGS':
          case 'DIRECT_REFERRAL':
          case 'BINARY_BONUS':
          case 'DEPOSIT':
            balance += transaction.amount;
            break;
          case 'WITHDRAWAL':
          case 'PURCHASE':
            balance -= transaction.amount;
            break;
        }
      }
    }

    // Check if user has sufficient balance (including fees)
    if (balance < totalDeduction) {
      return NextResponse.json(
        {
          success: false,
          error: `Insufficient balance. Required: $${totalDeduction.toFixed(2)} (Amount: $${amount} + Fees: $${totalFees.toFixed(2)}), Available: $${balance.toFixed(2)}`
        },
        { status: 400 }
      );
    }

    // Check for pending withdrawal requests
    const pendingWithdrawals = await withdrawalDb.findPending();
    const userPendingWithdrawals = pendingWithdrawals.filter(w => w.userId === user.id);
    
    if (userPendingWithdrawals.length > 0) {
      return NextResponse.json(
        { success: false, error: 'You have a pending withdrawal request. Please wait for it to be processed.' },
        { status: 400 }
      );
    }

    // Create withdrawal request
    const withdrawalRequest = await withdrawalDb.create({
      userId: user.id,
      amount: netAmount, // Store the net amount user will receive
      usdtAddress,
    });

    // Create withdrawal transaction (pending) - deduct total amount including fees
    await transactionDb.create({
      userId: user.id,
      type: 'WITHDRAWAL',
      amount: totalDeduction, // Deduct total amount including fees from balance
      description: `USDT withdrawal: $${netAmount} (Fees: $${totalFees.toFixed(2)}) to ${usdtAddress.slice(0, 8)}...${usdtAddress.slice(-8)}`,
      status: 'PENDING',
      reference: withdrawalRequest.id,
    });

    // Log the withdrawal request
    await systemLogDb.create({
      action: 'WITHDRAWAL_REQUESTED',
      userId: user.id,
      details: {
        withdrawalId: withdrawalRequest.id,
        requestedAmount: amount,
        netAmount: netAmount,
        totalFees: totalFees,
        fixedFee: fixedFee,
        percentageFee: percentageFeeAmount,
        totalDeduction: totalDeduction,
        usdtAddress,
        balanceAfter: balance - totalDeduction,
      },
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: {
        withdrawalId: withdrawalRequest.id,
        requestedAmount: amount,
        netAmount: netAmount,
        totalFees: totalFees,
        fixedFee: fixedFee,
        percentageFee: percentageFeeAmount,
        totalDeduction: totalDeduction,
        status: 'PENDING',
      },
    });

  } catch (error: any) {
    console.error('Withdrawal request error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to create withdrawal request' },
      { status: 500 }
    );
  }
}

// GET - Fetch user's withdrawal history
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    // Get user's withdrawal requests
    const withdrawals = await prisma.withdrawalRequest.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      take: 50,
    });

    const withdrawalHistory = withdrawals.map(w => ({
      id: w.id,
      amount: w.amount,
      usdtAddress: w.usdtAddress,
      status: w.status,
      txid: w.txid,
      createdAt: w.createdAt,
      processedAt: w.processedAt,
      rejectionReason: w.rejectionReason,
    }));

    return NextResponse.json({
      success: true,
      data: withdrawalHistory,
    });

  } catch (error: any) {
    console.error('Withdrawal history fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch withdrawal history' },
      { status: 500 }
    );
  }
}
