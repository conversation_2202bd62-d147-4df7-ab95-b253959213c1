const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugReferralAPI() {
  try {
    console.log('=== DEBUG REFERRAL API ===');
    
    // Find <EMAIL> user
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        directReferralCount: true,
        referralId: true
      }
    });

    if (!user) {
      console.log('User <EMAIL> not found');
      return;
    }

    console.log('\n1. USER INFO:');
    console.log('User ID:', user.id);
    console.log('Email:', user.email);
    console.log('Name:', user.firstName, user.lastName);
    console.log('Cached directReferralCount:', user.directReferralCount);
    console.log('Referral ID:', user.referralId);

    // Get direct referrals from referral table
    console.log('\n2. DIRECT REFERRALS FROM REFERRAL TABLE:');
    const directReferrals = await prisma.referral.findMany({
      where: { referrerId: user.id },
      include: {
        referred: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            isActive: true,
            createdAt: true
          }
        }
      }
    });

    console.log('Total direct referrals found:', directReferrals.length);
    directReferrals.forEach((ref, index) => {
      console.log(`${index + 1}. ${ref.referred.email} - ${ref.referred.firstName} ${ref.referred.lastName} - Side: ${ref.placementSide} - Active: ${ref.referred.isActive} - Created: ${ref.referred.createdAt}`);
    });

    // Get direct referrals from user table (referrerId field)
    console.log('\n3. DIRECT REFERRALS FROM USER TABLE (referrerId):');
    const directFromUserTable = await prisma.user.findMany({
      where: { referrerId: user.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        isActive: true,
        createdAt: true
      }
    });

    console.log('Total direct referrals from user table:', directFromUserTable.length);
    directFromUserTable.forEach((ref, index) => {
      console.log(`${index + 1}. ${ref.email} - ${ref.firstName} ${ref.lastName} - Active: ${ref.isActive} - Created: ${ref.createdAt}`);
    });

    // Check if there's a mismatch
    console.log('\n4. ANALYSIS:');
    console.log('Referral table count:', directReferrals.length);
    console.log('User table count:', directFromUserTable.length);
    console.log('Cached count:', user.directReferralCount);
    
    if (directReferrals.length !== directFromUserTable.length) {
      console.log('⚠️  MISMATCH: Referral table and User table counts differ!');
    }
    
    if (directReferrals.length !== user.directReferralCount) {
      console.log('⚠️  MISMATCH: Cached directReferralCount is outdated!');
    }

    // Test the actual API logic
    console.log('\n5. SIMULATING API LOGIC:');
    const leftReferrals = directReferrals.filter(r => r.placementSide === 'LEFT').length;
    const rightReferrals = directReferrals.filter(r => r.placementSide === 'RIGHT').length;
    
    console.log('Left side referrals:', leftReferrals);
    console.log('Right side referrals:', rightReferrals);
    console.log('Total direct referrals (API logic):', directReferrals.length);

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugReferralAPI();
