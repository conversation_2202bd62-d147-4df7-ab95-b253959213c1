'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent, Button } from '@/components/ui';
import { Grid } from '@/components/layout';
import { 
  Users, Copy, Share2, TrendingUp, Award, Search, ZoomIn, ZoomOut, 
  Maximize2, ChevronDown, ChevronRight, User, Calendar, Crown,
  Activity, Target, BarChart3
} from 'lucide-react';
import { formatCurrency, formatDate, copyToClipboard, getTimeUntilBinaryPayout } from '@/lib/utils';

interface EnhancedBinaryTreeNode {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
    isActive: boolean;
  };
  sponsorInfo?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  directReferralCount: number;
  teamCounts: {
    left: number;
    right: number;
    total: number;
  };
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
    matchedPoints: number;
  };
  hasLeftChild?: boolean;
  hasRightChild?: boolean;
  leftChild?: EnhancedBinaryTreeNode;
  rightChild?: EnhancedBinaryTreeNode;
}

interface EnhancedBinaryTreeData {
  treeStructure: EnhancedBinaryTreeNode;
  statistics: {
    totalDirectReferrals: number;
    leftReferrals: number;
    rightReferrals: number;
    totalCommissions: number;
    detailedStats: {
      directReferrals: number;
      leftTeam: number;
      rightTeam: number;
      totalTeam: number;
      activeMembers: number;
      recentJoins: number;
    };
    treeHealth: {
      totalUsers: number;
      balanceRatio: number;
      averageDepth: number;
      maxDepth: number;
      emptyPositions: number;
    };
    binaryPoints: {
      leftPoints: number;
      rightPoints: number;
      matchedPoints: number;
    };
  };
  referralLinks: {
    left: string;
    right: string;
    general: string;
  };
}

interface TreeViewSettings {
  zoom: number;
  maxDepth: number;
  showInactive: boolean;
  expandedNodes: Set<string>;
  focusedNode: string | null;
}

// Add custom CSS for animations
const treeAnimationStyles = `
  @keyframes fadeInScale {
    0% {
      opacity: 0;
      transform: scale(0.8);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }
`;

export const AdvancedBinaryTreeVisualizer: React.FC = () => {
  const [treeData, setTreeData] = useState<EnhancedBinaryTreeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [timeUntilMatching, setTimeUntilMatching] = useState(getTimeUntilBinaryPayout());
  const [isMobile, setIsMobile] = useState(false);
  const [viewSettings, setViewSettings] = useState<TreeViewSettings>({
    zoom: 1,
    maxDepth: 3, // Default to 3 levels as requested
    showInactive: true,
    expandedNodes: new Set(),
    focusedNode: null,
  });

  const treeContainerRef = useRef<HTMLDivElement>(null);
  const [dragState, setDragState] = useState({
    isDragging: false,
    startX: 0,
    startY: 0,
    scrollLeft: 0,
    scrollTop: 0,
  });

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);

      // Auto-adjust depth for mobile
      if (mobile && viewSettings.maxDepth > 4) {
        setViewSettings(prev => ({ ...prev, maxDepth: 4 }));
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [viewSettings.maxDepth]);

  // Drag functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!treeContainerRef.current) return;

    setDragState({
      isDragging: true,
      startX: e.pageX - treeContainerRef.current.offsetLeft,
      startY: e.pageY - treeContainerRef.current.offsetTop,
      scrollLeft: treeContainerRef.current.scrollLeft,
      scrollTop: treeContainerRef.current.scrollTop,
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!dragState.isDragging || !treeContainerRef.current) return;

    e.preventDefault();
    const x = e.pageX - treeContainerRef.current.offsetLeft;
    const y = e.pageY - treeContainerRef.current.offsetTop;
    const walkX = (x - dragState.startX) * 2;
    const walkY = (y - dragState.startY) * 2;

    treeContainerRef.current.scrollLeft = dragState.scrollLeft - walkX;
    treeContainerRef.current.scrollTop = dragState.scrollTop - walkY;
  };

  const handleMouseUp = () => {
    setDragState(prev => ({ ...prev, isDragging: false }));
  };

  useEffect(() => {
    fetchTreeData();
    
    // Update countdown every second
    const interval = setInterval(() => {
      setTimeUntilMatching(getTimeUntilBinaryPayout());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchTreeData = useCallback(async () => {
    try {
      setLoading(true);
      const expandedNodesParam = Array.from(viewSettings.expandedNodes).join(',');
      const response = await fetch(`/api/referrals/tree?depth=${viewSettings.maxDepth}&enhanced=true&expanded=${expandedNodesParam}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setTreeData(data.data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch enhanced binary tree data:', error);
    } finally {
      setLoading(false);
    }
  }, [viewSettings.maxDepth, viewSettings.expandedNodes]);

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const response = await fetch(`/api/referrals/search?term=${encodeURIComponent(searchTerm)}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setSearchResults(data.data);
        }
      }
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  const handleZoom = (direction: 'in' | 'out') => {
    setViewSettings(prev => ({
      ...prev,
      zoom: direction === 'in' 
        ? Math.min(prev.zoom * 1.2, 3) 
        : Math.max(prev.zoom / 1.2, 0.5)
    }));
  };

  const toggleNodeExpansion = useCallback(async (nodeId: string) => {
    console.log('Toggling expansion for node:', nodeId);

    setViewSettings(prev => {
      const newExpanded = new Set(prev.expandedNodes);
      if (newExpanded.has(nodeId)) {
        console.log('Collapsing node:', nodeId);
        newExpanded.delete(nodeId);
      } else {
        console.log('Expanding node:', nodeId);
        newExpanded.add(nodeId);
      }
      console.log('New expanded nodes:', Array.from(newExpanded));
      return { ...prev, expandedNodes: newExpanded };
    });

    // Refetch tree data with updated expanded nodes
    setTimeout(() => {
      fetchTreeData();
    }, 100);
  }, [fetchTreeData]);

  const focusOnNode = (nodeId: string) => {
    setViewSettings(prev => ({ ...prev, focusedNode: nodeId }));
  };

  const handleCopyLink = async (link: string) => {
    try {
      await copyToClipboard(link);
      // You could add a toast notification here
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  // Calculate responsive dimensions with better auto-scaling
  const getResponsiveDimensions = useCallback(() => {
    const baseNodeWidth = isMobile ? 140 : 180;
    const baseNodeHeight = isMobile ? 120 : 140;
    const minSpacing = isMobile ? 20 : 30;

    // Calculate the maximum depth that has nodes
    const getMaxActiveDepth = (node: EnhancedBinaryTreeNode | null, depth = 0): number => {
      if (!node) return depth;
      const leftDepth = node.leftChild ? getMaxActiveDepth(node.leftChild, depth + 1) : depth;
      const rightDepth = node.rightChild ? getMaxActiveDepth(node.rightChild, depth + 1) : depth;
      return Math.max(leftDepth, rightDepth);
    };

    const maxActiveDepth = treeData ? getMaxActiveDepth(treeData.treeStructure) : 3;
    const effectiveDepth = Math.min(maxActiveDepth + 1, viewSettings.maxDepth);

    // Calculate optimal spacing based on available width and number of nodes at deepest level
    const maxNodesAtLevel = Math.pow(2, effectiveDepth - 1);
    const availableWidth = window.innerWidth - 100; // Account for padding
    const optimalNodeSpacing = Math.max(
      minSpacing,
      (availableWidth - (maxNodesAtLevel * baseNodeWidth)) / Math.max(1, maxNodesAtLevel - 1)
    );

    const containerWidth = Math.max(
      availableWidth,
      maxNodesAtLevel * baseNodeWidth + (maxNodesAtLevel - 1) * optimalNodeSpacing
    );

    return {
      containerWidth,
      nodeWidth: baseNodeWidth,
      nodeHeight: baseNodeHeight,
      horizontalSpacing: Math.min(optimalNodeSpacing, 250), // Cap maximum spacing
      verticalSpacing: isMobile ? 140 : 180,
    };
  }, [isMobile, viewSettings.maxDepth, treeData]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-96 bg-gray-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (!treeData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load enhanced binary tree data</p>
          <Button
            onClick={fetchTreeData}
            className="mt-4"
            variant="outline"
          >
            Retry Loading
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {/* Inject custom CSS */}
      <style dangerouslySetInnerHTML={{ __html: treeAnimationStyles }} />

      {/* Enhanced Statistics Dashboard */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">🚀 Advanced Network Analytics</h2>
        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>
          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Direct Referrals</p>
                  <p className="text-3xl font-bold text-dark-900">
                    {treeData.statistics?.detailedStats?.directReferrals || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    +{treeData.statistics?.detailedStats?.recentJoins || 0} this month
                  </p>
                </div>
                <div className="h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center">
                  <Users className="h-7 w-7 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Team</p>
                  <p className="text-3xl font-bold text-eco-600">
                    {treeData.statistics?.detailedStats?.totalTeam || 0}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {treeData.statistics?.detailedStats?.activeMembers || 0} active
                  </p>
                </div>
                <div className="h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center">
                  <Target className="h-7 w-7 text-eco-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Tree Balance</p>
                  <p className="text-3xl font-bold text-solar-600">
                    {Math.round((treeData.statistics?.treeHealth?.balanceRatio || 0) * 100)}%
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    L: {treeData.statistics?.detailedStats?.leftTeam || 0} | R: {treeData.statistics?.detailedStats?.rightTeam || 0}
                  </p>
                </div>
                <div className="h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center">
                  <BarChart3 className="h-7 w-7 text-solar-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-2">Total Commissions</p>
                  <p className="text-3xl font-bold text-green-600">
                    {formatCurrency(treeData.statistics?.totalCommissions || 0)}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">All time earnings</p>
                </div>
                <div className="h-14 w-14 bg-green-100 rounded-xl flex items-center justify-center">
                  <TrendingUp className="h-7 w-7 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Grid>
      </div>

      {/* Tree Controls and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-500" />
              <span>Interactive Binary Tree</span>
            </span>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleZoom('out')}
                disabled={viewSettings.zoom <= 0.5}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-gray-600 min-w-[60px] text-center">
                {Math.round(viewSettings.zoom * 100)}%
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleZoom('in')}
                disabled={viewSettings.zoom >= 3}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setViewSettings(prev => ({ ...prev, zoom: 1 }))}
              >
                <Maximize2 className="h-4 w-4" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search Bar */}
          <div className="mb-6">
            <div className="flex space-x-2">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <Button onClick={handleSearch}>
                Search
              </Button>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Search Results:</h4>
                <div className="space-y-2">
                  {searchResults.map((result) => (
                    <div
                      key={result.id}
                      className="flex items-center justify-between p-2 bg-white rounded border cursor-pointer hover:bg-blue-50"
                      onClick={() => focusOnNode(result.id)}
                    >
                      <div>
                        <p className="text-sm font-medium">
                          {result.firstName} {result.lastName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {result.email} • Path: {result.placementPath} • Gen: {result.generation}
                        </p>
                      </div>
                      <Button size="sm" variant="outline">
                        Focus
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Tree Display Settings */}
          <div className="mb-6 flex flex-wrap items-center gap-4">

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="showInactive"
                checked={viewSettings.showInactive}
                onChange={(e) => setViewSettings(prev => ({ ...prev, showInactive: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="showInactive" className="text-sm font-medium text-gray-700">
                Show Inactive Users
              </label>
            </div>

            {/* Zoom Controls */}
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Zoom:</label>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setViewSettings(prev => ({
                  ...prev,
                  zoom: Math.max(0.5, prev.zoom - 0.1)
                }))}
                disabled={viewSettings.zoom <= 0.5}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm text-gray-600 min-w-[60px] text-center">
                {Math.round(viewSettings.zoom * 100)}%
              </span>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setViewSettings(prev => ({
                  ...prev,
                  zoom: Math.min(2, prev.zoom + 0.1)
                }))}
                disabled={viewSettings.zoom >= 2}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setViewSettings(prev => ({
                  ...prev,
                  expandedNodes: new Set(),
                  focusedNode: null
                }))}
              >
                Collapse All
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={fetchTreeData}
              >
                Refresh Tree
              </Button>

              <Button
                size="sm"
                variant="outline"
                onClick={async () => {
                  try {
                    const response = await fetch('/api/admin/refresh-tree-cache', {
                      method: 'POST',
                      credentials: 'include',
                    });
                    if (response.ok) {
                      await fetchTreeData();
                    }
                  } catch (error) {
                    console.error('Failed to refresh cache:', error);
                  }
                }}
              >
                Refresh Cache
              </Button>
            </div>

            {/* Tree Stats */}
            <div className="ml-auto flex items-center space-x-4 text-sm text-gray-600">
              <span>
                Showing: {Math.min(Math.pow(2, viewSettings.maxDepth) - 1, treeData.statistics?.detailedStats?.totalTeam || 0)} nodes
              </span>
              <span>
                Balance: {Math.round((treeData.statistics?.treeHealth?.balanceRatio || 0) * 100)}%
              </span>
            </div>
          </div>

          {/* Interactive MLM Tree Visualization */}
          <div
            ref={treeContainerRef}
            className={`relative overflow-auto border border-gray-200 rounded-lg bg-gradient-to-br from-slate-50 to-blue-50 ${
              dragState.isDragging ? 'cursor-grabbing' : 'cursor-grab'
            }`}
            style={{ height: isMobile ? '500px' : '700px' }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {loading && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                  <p className="text-gray-600">Loading MLM network tree...</p>
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="absolute top-4 left-4 bg-white bg-opacity-90 rounded-lg p-3 text-sm text-gray-600 z-40">
              <p className="font-medium mb-1">💡 Navigation Tips:</p>
              <p>• Click & drag to move around</p>
              <p>• Use zoom controls to scale</p>
              <p>• Click nodes to focus/expand</p>
            </div>

            <div
              className="relative min-w-max min-h-full pt-8"
              style={{
                transform: `scale(${viewSettings.zoom})`,
                transformOrigin: 'top center',
              }}
            >
              {(() => {
                // Use improved responsive dimensions
                const dimensions = getResponsiveDimensions();
                const { nodeWidth, nodeHeight, horizontalSpacing, verticalSpacing, containerWidth } = dimensions;

                return (
                  <div
                    className="relative"
                    style={{
                      width: `${containerWidth}px`,
                      height: `${viewSettings.maxDepth * verticalSpacing + nodeHeight + 100}px`
                    }}
                  >
                    {/* SVG for connections */}
                    <svg
                      className="absolute inset-0 w-full h-full pointer-events-none"
                      style={{ zIndex: 1 }}
                      viewBox={`0 0 ${containerWidth} ${viewSettings.maxDepth * verticalSpacing + nodeHeight + 100}`}
                      preserveAspectRatio="xMidYMid meet"
                    >
                      <defs>
                        <filter id="glow">
                          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                          <feMerge>
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                          </feMerge>
                        </filter>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7"
                          refX="9" refY="3.5" orient="auto">
                          <polygon points="0 0, 10 3.5, 0 7" fill="#10b981" />
                        </marker>
                      </defs>
                      <BinaryTreeConnections
                        treeData={treeData.treeStructure}
                        viewSettings={viewSettings}
                        nodeWidth={nodeWidth}
                        nodeHeight={nodeHeight}
                        horizontalSpacing={horizontalSpacing}
                        verticalSpacing={verticalSpacing}
                        containerWidth={containerWidth}
                      />
                    </svg>

                    {/* Nodes */}
                    <div className="relative" style={{ zIndex: 2 }}>
                      <BinaryTreeNodeComponent
                        node={treeData.treeStructure}
                        nodeIndex={0}
                        depth={0}
                        maxDepth={viewSettings.maxDepth}
                        viewSettings={viewSettings}
                        onToggleExpansion={toggleNodeExpansion}
                        onFocusNode={focusOnNode}
                        nodeWidth={nodeWidth}
                        nodeHeight={nodeHeight}
                        horizontalSpacing={horizontalSpacing}
                        verticalSpacing={verticalSpacing}
                        containerWidth={containerWidth}
                        isMobile={isMobile}
                      />
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Referral Links and Binary Matching */}
      <Grid cols={{ default: 1, lg: 2 }} gap={6}>
        {/* Next Binary Matching */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="h-5 w-5 text-solar-500" />
              <span>Next Binary Matching</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Daily binary matching at 12:00 AM UTC
              </p>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.hours}
                  </div>
                  <div className="text-xs text-gray-500">Hours</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.minutes}
                  </div>
                  <div className="text-xs text-gray-500">Minutes</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-solar-600">
                    {timeUntilMatching.seconds}
                  </div>
                  <div className="text-xs text-gray-500">Seconds</div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-solar-50 rounded-lg">
                <p className="text-sm text-solar-700">
                  <strong>Potential Match:</strong> {Math.min(treeData.statistics?.binaryPoints?.leftPoints || 0, treeData.statistics?.binaryPoints?.rightPoints || 0)} points
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Referral Links */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Share2 className="h-5 w-5 text-eco-500" />
              <span>Referral Links</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700">General Referral Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.general}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.general)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Left Side Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.left}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.left)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">Right Side Link</label>
                <div className="flex mt-1">
                  <input
                    type="text"
                    value={treeData.referralLinks.right}
                    readOnly
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50"
                  />
                  <Button
                    size="sm"
                    onClick={() => handleCopyLink(treeData.referralLinks.right)}
                    className="rounded-l-none"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-xs text-blue-700">
                <strong>Enhanced Placement:</strong> New users are automatically placed in your weaker leg
                for optimal network balance. Use specific side links to target placement.
              </p>
            </div>
          </CardContent>
        </Card>
      </Grid>
    </div>
  );
};

// Binary tree positioning with proper spacing
interface TreeNodePosition {
  x: number;
  y: number;
  depth: number;
}

const calculateTreePosition = (
  nodeIndex: number,
  depth: number,
  nodeWidth: number,
  nodeHeight: number,
  horizontalSpacing: number,
  verticalSpacing: number,
  containerWidth: number
): TreeNodePosition => {
  if (depth === 0) {
    // Root node - center of container
    return {
      x: (containerWidth - nodeWidth) / 2,
      y: 50,
      depth
    };
  }

  // Calculate horizontal position for binary tree with improved spacing
  const nodesAtLevel = Math.pow(2, depth);

  // Use dynamic spacing that adapts to container width
  const availableWidth = containerWidth - 40; // Account for margins
  const totalNodeWidth = nodesAtLevel * nodeWidth;
  const totalSpacingWidth = (nodesAtLevel - 1) * horizontalSpacing;

  // If nodes don't fit, reduce spacing proportionally
  let effectiveSpacing = horizontalSpacing;
  if (totalNodeWidth + totalSpacingWidth > availableWidth) {
    effectiveSpacing = Math.max(20, (availableWidth - totalNodeWidth) / Math.max(1, nodesAtLevel - 1));
  }

  const totalSpaceForLevel = nodesAtLevel * nodeWidth + (nodesAtLevel - 1) * effectiveSpacing;
  const startX = (containerWidth - totalSpaceForLevel) / 2;

  // Calculate position in current level
  const firstNodeAtLevel = Math.pow(2, depth) - 1;
  const positionInLevel = nodeIndex - firstNodeAtLevel;

  const x = Math.max(0, startX + positionInLevel * (nodeWidth + effectiveSpacing));
  const y = depth * verticalSpacing + 50;

  return { x, y, depth };
};

// Binary Tree Connections Component
interface BinaryTreeConnectionsProps {
  treeData: EnhancedBinaryTreeNode;
  viewSettings: TreeViewSettings;
  nodeWidth: number;
  nodeHeight: number;
  horizontalSpacing: number;
  verticalSpacing: number;
  containerWidth: number;
}

const BinaryTreeConnections: React.FC<BinaryTreeConnectionsProps> = ({
  treeData,
  viewSettings,
  nodeWidth,
  nodeHeight,
  horizontalSpacing,
  verticalSpacing,
  containerWidth,
}) => {
  const renderConnections = (
    node: EnhancedBinaryTreeNode | null,
    nodeIndex: number,
    depth: number
  ): JSX.Element[] => {
    if (!node || depth >= viewSettings.maxDepth - 1) return [];

    const isExpanded = viewSettings.expandedNodes.has(node.user.id);
    const shouldShowChildren = depth < 3 || isExpanded; // Show first 3 levels by default

    if (!shouldShowChildren) return [];

    const connections: JSX.Element[] = [];
    const currentPos = calculateTreePosition(
      nodeIndex,
      depth,
      nodeWidth,
      nodeHeight,
      horizontalSpacing,
      verticalSpacing,
      containerWidth
    );

    const currentCenterX = currentPos.x + nodeWidth / 2;
    const currentBottomY = currentPos.y + nodeHeight;

    // Left child connection
    if (node.leftChild && (viewSettings.showInactive || node.leftChild.user.isActive)) {
      const leftChildIndex = nodeIndex * 2 + 1; // Left child in binary tree
      const leftChildPos = calculateTreePosition(
        leftChildIndex,
        depth + 1,
        nodeWidth,
        nodeHeight,
        horizontalSpacing,
        verticalSpacing,
        containerWidth
      );
      const leftChildCenterX = leftChildPos.x + nodeWidth / 2;
      const leftChildTopY = leftChildPos.y;
      const midY = currentBottomY + (leftChildTopY - currentBottomY) / 2;

      connections.push(
        <g key={`left-${node.user.id}-${leftChildIndex}`}>
          {/* Background line for better visibility */}
          <line
            x1={currentCenterX}
            y1={currentBottomY}
            x2={leftChildCenterX}
            y2={leftChildTopY}
            stroke="#ffffff"
            strokeWidth="5"
            opacity="0.8"
          />
          {/* Main connection line */}
          <line
            x1={currentCenterX}
            y1={currentBottomY}
            x2={leftChildCenterX}
            y2={leftChildTopY}
            stroke={node.leftChild.user.isActive ? "#10b981" : "#6b7280"}
            strokeWidth={node.leftChild.user.isActive ? "3" : "2"}
            strokeDasharray={node.leftChild.user.isActive ? "none" : "5,5"}
            opacity="1"
          />
        </g>
      );

      // Recursive connections for left child
      connections.push(...renderConnections(node.leftChild, leftChildIndex, depth + 1));
    }

    // Right child connection
    if (node.rightChild && (viewSettings.showInactive || node.rightChild.user.isActive)) {
      const rightChildIndex = nodeIndex * 2 + 2; // Right child in binary tree
      const rightChildPos = calculateTreePosition(
        rightChildIndex,
        depth + 1,
        nodeWidth,
        nodeHeight,
        horizontalSpacing,
        verticalSpacing,
        containerWidth
      );
      const rightChildCenterX = rightChildPos.x + nodeWidth / 2;
      const rightChildTopY = rightChildPos.y;
      const midY = currentBottomY + (rightChildTopY - currentBottomY) / 2;

      connections.push(
        <g key={`right-${node.user.id}-${rightChildIndex}`}>
          {/* Background line for better visibility */}
          <line
            x1={currentCenterX}
            y1={currentBottomY}
            x2={rightChildCenterX}
            y2={rightChildTopY}
            stroke="#ffffff"
            strokeWidth="5"
            opacity="0.8"
          />
          {/* Main connection line */}
          <line
            x1={currentCenterX}
            y1={currentBottomY}
            x2={rightChildCenterX}
            y2={rightChildTopY}
            stroke={node.rightChild.user.isActive ? "#f59e0b" : "#6b7280"}
            strokeWidth={node.rightChild.user.isActive ? "3" : "2"}
            strokeDasharray={node.rightChild.user.isActive ? "none" : "5,5"}
            opacity="1"
          />
        </g>
      );

      // Recursive connections for right child
      connections.push(...renderConnections(node.rightChild, rightChildIndex, depth + 1));
    }

    return connections;
  };

  return <g>{renderConnections(treeData, 0, 0)}</g>;
};

// Binary Tree Node Component
interface BinaryTreeNodeProps {
  node: EnhancedBinaryTreeNode | null;
  nodeIndex: number;
  depth: number;
  maxDepth: number;
  viewSettings: TreeViewSettings;
  onToggleExpansion: (nodeId: string) => void;
  onFocusNode: (nodeId: string) => void;
  nodeWidth: number;
  nodeHeight: number;
  horizontalSpacing: number;
  verticalSpacing: number;
  containerWidth: number;
  isMobile: boolean;
}

const BinaryTreeNodeComponent: React.FC<BinaryTreeNodeProps> = ({
  node,
  nodeIndex,
  depth,
  maxDepth,
  viewSettings,
  onToggleExpansion,
  onFocusNode,
  nodeWidth,
  nodeHeight,
  horizontalSpacing,
  verticalSpacing,
  containerWidth,
  isMobile,
}) => {
  const renderNode = (
    currentNode: EnhancedBinaryTreeNode | null,
    currentIndex: number,
    currentDepth: number
  ): JSX.Element[] => {
    if (!currentNode || currentDepth >= maxDepth) return [];
    if (!viewSettings.showInactive && !currentNode.user.isActive) return [];

    const position = calculateTreePosition(
      currentIndex,
      currentDepth,
      nodeWidth,
      nodeHeight,
      horizontalSpacing,
      verticalSpacing,
      containerWidth
    );

    const isExpanded = viewSettings.expandedNodes.has(currentNode.user.id);
    const isFocused = viewSettings.focusedNode === currentNode.user.id;
    const hasChildren = currentNode.leftChild || currentNode.rightChild || currentNode.hasLeftChild || currentNode.hasRightChild;
    const shouldShowChildren = currentDepth < 3 || isExpanded; // Show first 3 levels by default (0, 1, 2)

    const nodes: JSX.Element[] = [];

    // Current node
    nodes.push(
      <div
        key={`node-${currentNode.user.id}`}
        className={`absolute bg-white border-2 rounded-xl shadow-lg transition-all duration-200 cursor-pointer hover:shadow-xl ${
          isFocused
            ? 'border-blue-500 ring-2 ring-blue-200'
            : currentNode.user.isActive
              ? 'border-green-400 hover:border-green-500'
              : 'border-gray-300 opacity-75'
        }`}
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${nodeWidth}px`,
          minHeight: `${nodeHeight}px`,
        }}
        onClick={() => onFocusNode(currentNode.user.id)}
      >
        {/* Expand/Collapse Button - Show for all nodes with children beyond level 2 */}
        {hasChildren && currentDepth >= 2 && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpansion(currentNode.user.id);
            }}
            className={`absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full flex items-center justify-center text-white text-xs shadow-md transition-all ${
              isExpanded
                ? 'bg-red-500 hover:bg-red-600'
                : 'bg-blue-500 hover:bg-blue-600'
            }`}
            title={isExpanded ? 'Collapse children' : 'Expand children'}
          >
            {isExpanded ? '−' : '+'}
          </button>
        )}

        <div className="p-3">
          {/* User Header */}
          <div className="flex items-center space-x-2 mb-3">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold ${
              currentNode.user.isActive ? 'bg-green-500' : 'bg-gray-400'
            }`}>
              {currentNode.user.firstName.charAt(0)}{currentNode.user.lastName.charAt(0)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold text-gray-900 truncate">
                {currentNode.user.firstName} {currentNode.user.lastName}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {currentNode.user.email}
              </p>
            </div>
          </div>

          {/* Team and Direct Stats */}
          <div className="grid grid-cols-2 gap-2 mb-3">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{currentNode.teamCounts.total}</div>
              <div className="text-xs text-blue-500">Team</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-purple-600">{currentNode.directReferralCount}</div>
              <div className="text-xs text-purple-500">Direct</div>
            </div>
          </div>

          {/* Left and Right Stats */}
          <div className="flex justify-between items-center mb-3">
            <div className="text-center">
              <div className="text-sm font-semibold text-green-600">{currentNode.teamCounts.left}</div>
              <div className="text-xs text-gray-500">Left</div>
            </div>
            <div className="text-xs text-gray-400">|</div>
            <div className="text-center">
              <div className="text-sm font-semibold text-orange-600">{currentNode.teamCounts.right}</div>
              <div className="text-xs text-gray-500">Right</div>
            </div>
          </div>

          {/* Points */}
          <div className="text-center mb-2">
            <span className="text-xs text-gray-600">
              Points: {currentNode.binaryPoints.leftPoints}/{currentNode.binaryPoints.rightPoints}
            </span>
          </div>

          {/* Status */}
          <div className="flex items-center justify-center space-x-1 mb-2">
            <Activity className={`h-3 w-3 ${currentNode.user.isActive ? 'text-green-500' : 'text-gray-400'}`} />
            <span className={`text-xs font-medium ${currentNode.user.isActive ? 'text-green-600' : 'text-gray-500'}`}>
              {currentNode.user.isActive ? 'Active' : 'Inactive'}
            </span>
            <span className="text-xs text-gray-400">
              {formatDate(currentNode.user.createdAt)}
            </span>
          </div>

          {/* Sponsor */}
          {currentNode.sponsorInfo && (
            <div className="flex items-center justify-center space-x-1">
              <Crown className="h-3 w-3 text-yellow-500" />
              <span className="text-xs text-gray-600 truncate">
                {currentNode.sponsorInfo.firstName} {currentNode.sponsorInfo.lastName}
              </span>
            </div>
          )}
        </div>
      </div>
    );

    // Render children if they should be shown
    if (shouldShowChildren && currentDepth < maxDepth - 1) {
      // Left child
      if (currentNode.leftChild) {
        nodes.push(...renderNode(currentNode.leftChild, currentIndex * 2 + 1, currentDepth + 1));
      }

      // Right child
      if (currentNode.rightChild) {
        nodes.push(...renderNode(currentNode.rightChild, currentIndex * 2 + 2, currentDepth + 1));
      }
    }

    return nodes;
  };

  return <>{renderNode(node, nodeIndex, depth)}</>;
};

